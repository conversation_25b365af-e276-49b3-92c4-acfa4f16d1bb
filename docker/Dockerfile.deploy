###############################################################################
#
# DO NOT EDIT!!!
#
# This file is used to deploy the https://test.talawa.io site
#
###############################################################################
FROM node:23.7.0 AS build
WORKDIR /usr/src/app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
ENV NODE_ENV=production
EXPOSE 4321
CMD ["npm", "run", "serve"]
