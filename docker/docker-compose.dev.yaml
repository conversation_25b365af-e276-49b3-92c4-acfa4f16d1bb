services:
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile.dev
    ports:
      - '4321:4321'
    env_file:
      - ../.env
    #environment:
    #  - REACT_APP_TALAWA_URL=${REACT_APP_TALAWA_URL}
    #  - REACT_APP_BACKEND_WEBSOCKET_URL=${REACT_APP_BACKEND_WEBSOCKET_URL}
    #  - PORT=${PORT}
    #  - REACT_APP_USE_RECAPTCHA=${REACT_APP_USE_RECAPTCHA}
    #  - REACT_APP_RECAPTCHA_SITE_KEY=${REACT_APP_RECAPTCHA_SITE_KEY}
    volumes:
      - ..:/usr/src/app
      - /usr/src/app/node_modules
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:4321"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
