services:
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile.prod
    ports:
      - '4321:4321'
    env_file:
      - ../.env
    #environment:
    #  - REACT_APP_TALAWA_URL=${REACT_APP_TALAWA_URL}
    #  - REACT_APP_BACKEND_WEBSOCKET_URL=${REACT_APP_BACKEND_WEBSOCKET_URL}
    #  - PORT=${PORT}
    #  - REACT_APP_USE_RECAPTCHA=${REACT_APP_USE_RECAPTCHA}
    #  - REACT_APP_RECAPTCHA_SITE_KEY=${REACT_APP_RECAPTCHA_SITE_KEY}
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:80']
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
