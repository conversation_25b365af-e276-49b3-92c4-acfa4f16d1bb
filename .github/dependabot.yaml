# Configuration for automated dependency updates using Dependabot
version: 2
updates:
  # Define the target package ecosystem
  - package-ecosystem: "npm"
    # Specify the root directory
    directory: "/"
    # Schedule automated updates to run weekly
    schedule:
      interval: "monthly"
    # Labels to apply to Dependabot PRs
    labels:
      - "dependencies"
    # Specify the target branch for PRs
    target-branch: "develop"
    # Customize commit message prefix
    commit-message:
      prefix: "chore(deps):"
