
<!--
This section can be deleted after reading.

We employ the following branching strategy to simplify the development process and to ensure that only stable code is pushed to the `master` branch:

- `develop`: For unstable code: New features and bug fixes.
- `master`: Where the stable production ready code lies. Only security related bugs.

NOTE!!!

ONLY SUBMIT PRS AGAINST OUR `DEVELOP` BRANCH. THE DEFAULT IS `MAIN`, SO YOU WILL HAVE TO MODIFY THIS BEFORE SUBMITTING YOUR PR FOR REVIEW. PRS MADE AGAINST `MAIN` WILL BE CLOSED.

-->

<!--
Thanks for submitting a pull request! Please provide enough information so that others can review your pull request.
-->

**What kind of change does this PR introduce?**

<!-- E.g. a bugfix, feature, refactoring, etc… -->

**Issue Number:**

Fixes #<!--Add related issue number here.-->

**Snapshots/Videos:**

<!--Add snapshots or videos wherever possible.-->

**If relevant, did you update the documentation?**

<!--Add link to Talawa-Docs.-->

**Summary**

<!-- Explain the **motivation** for making this change. What existing problem does the pull request solve? -->
<!-- Try to link to an open issue for more information. -->

**Does this PR introduce a breaking change?**

<!-- If this PR introduces a breaking change, please describe the impact and a migration path for existing applications. -->

## Checklist

### CodeRabbit AI Review
- [ ] I have reviewed and addressed all critical issues flagged by CodeRabbit AI
- [ ] I have implemented or provided justification for each non-critical suggestion
- [ ] I have documented my reasoning in the PR comments where CodeRabbit AI suggestions were not implemented

### Test Coverage
- [ ] I have written tests for all new changes/features
- [ ] I have verified that test coverage meets or exceeds 95%
- [ ] I have run the test suite locally and all tests pass


**Other information**

<!--Add extra information about this PR here-->

**Have you read the [contributing guide](https://github.com/PalisadoesFoundation/talawa-admin/blob/master/CONTRIBUTING.md)?**

<!--Yes or No-->