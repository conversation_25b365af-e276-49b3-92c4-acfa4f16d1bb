##############################################################################
##############################################################################
#
# NOTE!
#
# Please read the README.md file in this directory that defines what should 
# be placed in this file
#
##############################################################################
##############################################################################

name: codeql codescan workflow

on:
  pull_request:
    branches:
      - '**'  
  push:
    branches:
      - '**'   
jobs:
  CodeQL:
    if: ${{ github.actor != 'dependabot[bot]' }}
    name: Analyse Code With CodeQL
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        language: [ 'javascript' ]
    steps:
       - name: Checkout repository
         uses: actions/checkout@v4

       - name: Initialize CodeQL
         uses: github/codeql-action/init@v3
         with:
          languages: ${{ matrix.language }}
          debug: true

       - name: Autobuild
         uses: github/codeql-action/autobuild@v3

       - name: Perform CodeQL Analysis
         uses: github/codeql-action/analyze@v3
