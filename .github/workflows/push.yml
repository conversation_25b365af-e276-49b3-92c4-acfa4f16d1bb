##############################################################################
##############################################################################
#
# NOTE!
#
# Please read the README.md file in this directory that defines what should 
# be placed in this file
#
##############################################################################
##############################################################################

name: PUSH Workflow - All Branches

on:
  push:
    branches:
      - '**'   
      
env:
    CODECOV_UNIQUE_NAME: CODECOV_UNIQUE_NAME-${{ github.run_id }}-${{ github.run_number }}
  
jobs:
  Code-Coverage:
    if: ${{ github.actor != 'dependabot[bot]' }}
    name: Test and Calculate Code Coverage
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [22.x]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Cache node modules
        id: cache-npm
        uses: actions/cache@v4
        env:
          cache-name: cache-node-modules
        with:
          path: |
            ~/.npm
            node_modules
          key: ${{ runner.os }}-code-coverage-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-code-coverage-${{ env.cache-name }}-
            ${{ runner.os }}-code-coverage-
            ${{ runner.os }}-
      
      - if: ${{ steps.cache-npm.outputs.cache-hit != 'true' }}
        name: List the state of node modules
        run: npm install
        
      - name: Run Vitest Tests
        env:
          NODE_V8_COVERAGE: './coverage/vitest'
        run: |
          npm run test:coverage
        
      - name: Present and upload coverage to Codecov as ${{env.CODECOV_UNIQUE_NAME}}
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          verbose: true
          exclude: 'docs/'
          gcov_ignore: 'docs/'
          files: ./coverage/vitest/lcov.info
          fail_ci_if_error: false
          name: '${{env.CODECOV_UNIQUE_NAME}}'
