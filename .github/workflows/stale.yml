##############################################################################
##############################################################################
#
# NOTE!
#
# Please read the README.md file in this directory that defines what should 
# be placed in this file
#
##############################################################################
##############################################################################

name: Mark stale issues and pull requests

on:
  schedule:
    - cron: "0 0 * * *"

permissions:
  issues: write
  pull-requests: write  
  
jobs:
  stale:
    name: Process Stale Issues and PRs 
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: 'This issue did not get any activity in the past 10 days and will be closed in 180 days if no update occurs. Please check if the develop branch has fixed it and report again or close the issue.'
          stale-pr-message: 'This pull request did not get any activity in the past 10 days and will be closed in 180 days if no update occurs. Please verify it has no conflicts with the develop branch and rebase if needed. Mention it now if you need help or give permission to other people to finish your work.'
          close-issue-message: 'This issue did not get any activity in the past 180 days and thus has been closed. Please check if the newest release or develop branch has it fixed. Please, create a new issue if the issue is not fixed.'
          close-pr-message: 'This pull request did not get any activity in the past 180 days and thus has been closed.'
          stale-issue-label: 'no-issue-activity'
          stale-pr-label: 'no-pr-activity'
          days-before-stale: 7
          days-before-close: 180
          remove-stale-when-updated: true
          exempt-all-milestones: true
          exempt-pr-labels: 'wip'
          exempt-issue-labels: 'wip'
          operations-per-run: 30
