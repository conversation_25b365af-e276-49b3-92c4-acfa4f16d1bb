##############################################################################
##############################################################################
#
# NOTE!
#
# Please read the README.md file in this directory that defines what should 
# be placed in this file
#
##############################################################################
##############################################################################

name: PR Target Workflow
on: 
  pull_request_target:
  
jobs:
  PR-Greeting:
    name: Pull Request Target
    runs-on: ubuntu-latest
    steps:
      - name: Add the PR Review Policy
        uses: thollander/actions-comment-pull-request@v3
        with:
          comment-tag: pr_review_policy
          message: |
            ## Our Pull Request Approval Process
            
            Thanks for contributing! 
            
            ### Testing Your Code
            Remember, your PRs won't be reviewed until these criteria are met:

            1. We don't merge PRs with poor code quality. 
                1. Follow coding best practices such that CodeRabbit.ai approves your PR.
            1. We don't merge PRs with failed tests. 
                1. When tests fail, click on the `Details` link to learn more.
                1. Write sufficient tests for your changes (CodeCov Patch Test). Your testing level must be better than the target threshold of the repository
                1. Tests may fail if you edit sensitive files. Ask to add the `ignore-sensitive-files-pr` label if the edits are necessary.
            1. We cannot merge PRs with conflicting files. These must be fixed.
                
            Our policies make our code better.

            ### Reviewers

            Do not assign reviewers. Our Queue Monitors will review your PR and assign them. 
            When your PR has been assigned reviewers contact them to get your code reviewed and approved via:

            1. comments in this PR or
            1. our slack channel

            #### Reviewing Your Code

            Your reviewer(s) will have the following roles:

            1.  arbitrators of future discussions with other contributors about the validity of your changes
            2.  point of contact for evaluating the validity of your work
            3.  person who verifies matching issues by others that should be closed.
            4.  person who gives general guidance in fixing your tests

            ### CONTRIBUTING.md

            Read our CONTRIBUTING.md file. Most importantly:

            1. PRs with issues not assigned to you will be closed by the reviewer
            1. Fix the first comment in the PR so that each issue listed automatically closes

            ### Other

            1. :dart: Please be considerate of our volunteers' time. Contacting the person who assigned the reviewers is not advised unless they ask for your input. Do not @ the person who did the assignment otherwise.
            2. Read the CONTRIBUTING.md file make 

      - name: Greeting Message to User
        uses: actions/first-interaction@v1
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          pr-message: "Congratulations on making your first PR! :confetti_ball: If you haven't already, check out our [Contributing Guidelines](https://github.com/PalisadoesFoundation/talawa-admin/blob/-/CONTRIBUTING.md) and [PR Reporting Guidelines](https://github.com/PalisadoesFoundation/talawa-admin/blob/-/PR_GUIDELINES.md) to ensure that you are following our guidelines for contributing and creating PR."
