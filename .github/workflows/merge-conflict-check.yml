name: Merge Conflict Check Workflow

on:
  pull_request:
    branches:
      - '**'
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review

jobs:
  Merge-Conflict-Check:
    name: Check for Merge Conflicts
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Check Mergeable Status via Github API
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
            PR_NUMBER=${{ github.event.pull_request.number }}
            max_retries=3
            retry_delay=5

            for ((i=1; i<=max_retries; i++)); do
              echo "Attempt $i of $max_retries"

              if ! response=$(gh api "repos/${{ github.repository }}/pulls/$PR_NUMBER" --jq '.mergeable'); then
                if [[ $response == *"rate limit exceeded"* ]]; then
                  echo "Rate limit exceeded. Waiting before retry..."
                  sleep 60  # Wait longer for rate limit
                else
                  echo "Failed to call GitHub API: $response"
                  if [ $i -eq $max_retries ]; then
                    echo "Maximum retries reached. Exiting."
                    exit 1
                  fi
                  sleep $retry_delay
                fi
                continue
              fi
              
              case "$response" in
                "true")
                  echo "No conflicts detected."
                  exit 0
                  ;;
                "false")
                  echo "Merge conflicts detected."
                  exit 1
                  ;;
                *)
                  echo "Mergeable status unknown: $response"
                  if [ $i -eq $max_retries ]; then
                    echo "Maximum retries reached. Exiting."
                    exit 1
                  fi
                  sleep $retry_delay
                  ;;
              esac
            done
