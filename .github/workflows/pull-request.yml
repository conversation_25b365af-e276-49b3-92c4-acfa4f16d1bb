##############################################################################
##############################################################################
#
# NOTE!
#
# Please read the README.md file in this directory that defines what should
# be placed in this file
#
##############################################################################
##############################################################################

name: PR Workflow

on:
  pull_request:
    branches:
      - '**'

env:
  CODECOV_UNIQUE_NAME: CODECOV_UNIQUE_NAME-${{ github.run_id }}-${{ github.run_number }}

jobs:
  Code-Quality-Checks:
    name: Performs linting, formatting, type-checking, checking for different source and target branch
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history for all branches and tags

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.x'

      - name: Install Dependencies
        run: npm install

      - name: Count number of lines
        run: |
          chmod +x ./.github/workflows/scripts/countline.py
          ./.github/workflows/scripts/countline.py --lines 600 --exclude_files src/screens/LoginPage/LoginPage.tsx src/GraphQl/Queries/Queries.ts src/screens/OrgList/OrgList.tsx src/GraphQl/Mutations/mutations.ts src/components/EventListCard/EventListCardModals.tsx src/components/TagActions/TagActionsMocks.ts src/utils/interfaces.ts src/screens/MemberDetail/MemberDetail.tsx src/components/OrgPostCard/OrgPostCard.tsx src/components/UsersTableItem/UsersTableItem.tsx

      - name: Get changed TypeScript files
        id: changed-files
        run: |
          # Get the base branch ref
          BASE_SHA=$(git merge-base ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }})

          # Get all changed files
          ALL_CHANGED_FILES=$(git diff --name-only --diff-filter=ACMRT $BASE_SHA ${{ github.event.pull_request.head.sha }} | tr '\n' ' ')
          echo "all_changed_files=${ALL_CHANGED_FILES}" >> $GITHUB_OUTPUT

          # Count all changed files
          ALL_CHANGED_FILES_COUNT=$(git diff --name-only --diff-filter=ACMRT $BASE_SHA ${{ github.event.pull_request.head.sha }} | wc -l | tr -d ' ')
          echo "all_changed_files_count=$ALL_CHANGED_FILES_COUNT" >> $GITHUB_OUTPUT

          # Check if any files changed
          if [ "$ALL_CHANGED_FILES_COUNT" -gt 0 ]; then
            echo "any_changed=true" >> $GITHUB_OUTPUT
          else
            echo "any_changed=false" >> $GITHUB_OUTPUT
          fi

          # Set only_changed to false by default (adjust logic as needed)
          echo "only_changed=false" >> $GITHUB_OUTPUT

      - name: Check formatting
        if: steps.changed-files.outputs.only_changed != 'true'
        run: npm run format:check
        
      - name: Run formatting if check fails
        if: failure()
        run: npm run format:fix

      - name: Check for type errors
        if: steps.changed-files.outputs.only_changed != 'true'
        run: npm run typecheck

      - name: Check for linting errors in modified files
        if: steps.changed-files.outputs.only_changed != 'true'
        env: 
          CHANGED_FILES: ${{ steps.changed-files.outputs.all_changed_files }}
        run: npx eslint ${CHANGED_FILES}
        
      - name: Check for TSDoc comments
        run: npm run check-tsdoc # Run the TSDoc check script

      - name: Check for localStorage Usage
        run: npx tsx scripts/githooks/check-localstorage-usage.ts --scan-entire-repo

      - name: Compare translation files
        run: |
          chmod +x .github/workflows/scripts/compare_translations.py
          python .github/workflows/scripts/compare_translations.py --directory public/locales

      - name: Check if the source and target branches are different
        if: ${{ github.event.pull_request.base.ref == github.event.pull_request.head.ref }}
        run: |
          echo "Source Branch ${{ github.event.pull_request.head.ref }}"
          echo "Target Branch ${{ github.event.pull_request.base.ref }}"
          echo "Error: Source and Target Branches are the same. Please ensure they are different."
          echo "Error: Close this PR and try again."
          exit 1

  Check-Sensitive-Files:
    if: ${{ github.actor != 'dependabot[bot]' && !contains(github.event.pull_request.labels.*.name, 'ignore-sensitive-files-pr') }}
    name: Checks if sensitive files have been changed without authorization
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history for all branches and tags

      - name: Get Changed Unauthorized files
        id: changed-unauth-files
        run: |
          # Get the base branch ref
          BASE_SHA=$(git merge-base ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }})

          # Define sensitive files pattern
          SENSITIVE_FILES=".flake8 .pydocstyle pyproject.toml .env* vitest.config.js src/App.tsx .github/** env.example .node-version .husky/** scripts/** src/style/** schema.graphql package.json package-lock.json tsconfig.json .gitignore .eslintrc.json .eslintignore .prettierrc .prettierignore vite.config.ts docker/docker-compose.prod.yaml docker/docker-compose.dev.yaml docker/Dockerfile.dev docker/Dockerfile.prod config/docker/setup/nginx.conf config/docker/setup/nginx.prod.conf CODEOWNERS LICENSE setup.ts .coderabbit.yaml CODE_OF_CONDUCT.md CODE_STYLE.md CONTRIBUTING.md DOCUMENTATION.md INSTALLATION.md ISSUE_GUIDELINES.md PR_GUIDELINES.md README.md *.pem *.key *.cert *.password *.secret *.credentials .nojekyll yarn.lock docs/docusaurus.config.ts docs/sidebar* CNAME"

          # Check for changes in sensitive files
          CHANGED_UNAUTH_FILES=""
          for pattern in $SENSITIVE_FILES; do
            FILES=$(git diff --name-only --diff-filter=ACMRD $BASE_SHA ${{ github.event.pull_request.head.sha }} | grep -E "$pattern" || true)
            if [ ! -z "$FILES" ]; then
              CHANGED_UNAUTH_FILES="$CHANGED_UNAUTH_FILES $FILES"
            fi
          done

          # Trim and format output
          CHANGED_UNAUTH_FILES=$(echo "$CHANGED_UNAUTH_FILES" | xargs)
          echo "all_changed_files=$CHANGED_UNAUTH_FILES" >> $GITHUB_OUTPUT

          # Check if any unauthorized files changed
          if [ ! -z "$CHANGED_UNAUTH_FILES" ]; then
            echo "any_changed=true" >> $GITHUB_OUTPUT
            echo "any_deleted=true" >> $GITHUB_OUTPUT
          else
            echo "any_changed=false" >> $GITHUB_OUTPUT
            echo "any_deleted=false" >> $GITHUB_OUTPUT
          fi

      - name: List all changed unauthorized files
        if: steps.changed-unauth-files.outputs.any_changed == 'true' || steps.changed-unauth-files.outputs.any_deleted == 'true'
        env: 
          CHANGED_UNAUTH_FILES: ${{ steps.changed-unauth-files.outputs.all_changed_files }}
        run: |
          for file in ${CHANGED_UNAUTH_FILES}; do
            echo "$file is unauthorized to change/delete"
          done
          echo "To override this, apply the 'ignore-sensitive-files-pr' label"          
          exit 1

  Count-Changed-Files:
    if: ${{ github.actor != 'dependabot[bot]' }}
    name: Checks if number of files changed is acceptable
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history for all branches and tags

      - name: Get changed files
        id: changed-files
        run: |
          # Get the base branch ref
          BASE_SHA=$(git merge-base ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }})

          # Count all changed files excluding .md files
          ALL_CHANGED_FILES_COUNT=$(git diff --name-only --diff-filter=ACMRT $BASE_SHA ${{ github.event.pull_request.head.sha }} | grep -v -i "\.md$" | wc -l | tr -d ' ')
          echo "all_changed_files_count=$ALL_CHANGED_FILES_COUNT" >> $GITHUB_OUTPUT

      - name: Echo number of changed files
        env: 
          CHANGED_FILES_COUNT: ${{ steps.changed-files.outputs.all_changed_files_count }}
        run: |
          echo "Number of files changed: $CHANGED_FILES_COUNT"

      - name: Check if the number of changed files is less than 100
        if: steps.changed-files.outputs.all_changed_files_count > 100
        env: 
          CHANGED_FILES_COUNT: ${{ steps.changed-files.outputs.all_changed_files_count }}
        run: |
          echo "Error: Too many files (greater than 100) changed in the pull request."
          echo "Possible issues:"
          echo "- Contributor may be merging into an incorrect branch."
          echo "- Source branch may be incorrect please use develop as source branch."
          exit 1

  Check-ESlint-Disable:
    name: Check for eslint-disable
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history for all branches and tags

      - name: Get changed files
        id: changed-files
        run: |
          # Get the base branch ref
          BASE_SHA=$(git merge-base ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }})

          # Get all changed files
          ALL_CHANGED_FILES=$(git diff --name-only --diff-filter=ACMRT $BASE_SHA ${{ github.event.pull_request.head.sha }} | tr '\n' ' ')
          echo "all_changed_files=${ALL_CHANGED_FILES}" >> $GITHUB_OUTPUT

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.9

      - name: Run Python script
        run: |
          python .github/workflows/scripts/eslint_disable_check.py --files ${{ steps.changed-files.outputs.all_changed_files }}

  Check-Code-Coverage-Disable:
    name: Check for code coverage disable
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history for all branches and tags

      - name: Get changed files
        id: changed-files
        run: |
          # Get the base branch ref
          BASE_SHA=$(git merge-base ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }})

          # Get all changed files
          ALL_CHANGED_FILES=$(git diff --name-only --diff-filter=ACMRT $BASE_SHA ${{ github.event.pull_request.head.sha }} | tr '\n' ' ')
          echo "all_changed_files=${ALL_CHANGED_FILES}" >> $GITHUB_OUTPUT

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.9

      - name: Run Python script
        run: |
          python .github/workflows/scripts/code_coverage_disable_check.py --files ${{ steps.changed-files.outputs.all_changed_files }} 

  Test-Application:
    name: Test Application
    runs-on: ubuntu-latest
    needs: [Code-Quality-Checks, Check-ESlint-Disable, Check-Code-Coverage-Disable]
    steps:
      - name: Checkout the Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.x'

      - name: Install Dependencies
        run: npm ci

      - name: Get changed TypeScript files
        id: changed-files
        run: |
          # Get the base branch ref
          BASE_SHA=$(git merge-base ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }})

          # Check if any files changed
          ANY_CHANGED=$(git diff --name-only --diff-filter=ACMRT $BASE_SHA ${{ github.event.pull_request.head.sha }} | wc -l)
          if [ "$ANY_CHANGED" -gt 0 ]; then
            echo "any_changed=true" >> $GITHUB_OUTPUT
          else
            echo "any_changed=false" >> $GITHUB_OUTPUT
          fi

          # Get all changed files
          ALL_FILES=$(git diff --name-only --diff-filter=ACMRT $BASE_SHA ${{ github.event.pull_request.head.sha }} | tr '\n' ' ')
          echo "all_files=$ALL_FILES" >> $GITHUB_OUTPUT

          # Get TypeScript files specifically
          TS_FILES=$(git diff --name-only --diff-filter=ACMRT $BASE_SHA ${{ github.event.pull_request.head.sha }} | grep -E '\.tsx?$' | tr '\n' ' ')
          echo "ts_files=$TS_FILES" >> $GITHUB_OUTPUT

      - name: TypeScript compilation
        run: |
          npx tsc --noEmit

      - name: Run Vitest Tests
        if: steps.changed-files.outputs.any_changed == 'true'
        env:
          NODE_V8_COVERAGE: './coverage/vitest'
          NODE_OPTIONS: "--max-old-space-size=6144"
        run: |
          npm run test:coverage

      - name: Upload Coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          exclude: 'docs/'
          files: ./coverage/vitest/lcov.info
          fail_ci_if_error: true

      - name: Test acceptable level of code coverage
        uses: VeryGoodOpenSource/very_good_coverage@v3
        with:
          path: "./coverage/vitest/lcov.info"
          min_coverage: 86.0

  # Graphql-Inspector:
  #   if: ${{ github.actor != 'dependabot[bot]' }}
  #   name: Runs Introspection on the GitHub talawa-api repo on the schema.graphql file
  #   runs-on: ubuntu-latest
  #   steps:
  #     - name: Checkout the Repository
  #       uses: actions/checkout@v4

  #     - name: Set up Node.js
  #       uses: actions/setup-node@v4
  #       with:
  #         node-version: '22.x'

  #     - name: resolve dependency
  #       run: npm install -g @graphql-inspector/cli
      
  #     - name: Clone API Repository
  #       run: |
  #         # Retrieve the complete branch name directly from the GitHub context
  #         FULL_BRANCH_NAME=${{ github.base_ref }}
  #         echo "FULL_Branch_NAME: $FULL_BRANCH_NAME"
          
  #         # Clone the specified repository using the extracted branch name
  #         git clone --branch $FULL_BRANCH_NAME https://github.com/PalisadoesFoundation/talawa-api && ls -a 

  #     - name: Validate Documents
  #       run: graphql-inspector validate './src/GraphQl/**/*.ts' './talawa-api/schema.graphql'

  Start-App-Without-Docker:
    name: Check if Talawa Admin app starts (No Docker)
    runs-on: ubuntu-latest
    needs: [Code-Quality-Checks, Test-Application]
    if: github.actor != 'dependabot'
    steps:
      - name: Checkout the Repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'

      - name: Install Dependencies
        run: npm install

      - name: Build Production App
        run: npm run build

      - name: Start Production App
        run: |
          npm run preview &
          echo $! > .pidfile_prod
      - name: Check if Production App is running
        run: |
          chmod +x .github/workflows/scripts/app_health_check.sh
          .github/workflows/scripts/app_health_check.sh 4173 120
      - name: Stop Production App
        run: |
          if [ -f .pidfile_prod ]; then
            kill "$(cat .pidfile_prod)"
          fi
      - name: Start Development App
        run: |
          npm run serve &
          echo $! > .pidfile_dev
      - name: Check if Development App is running
        run: |
          chmod +x .github/workflows/scripts/app_health_check.sh
          .github/workflows/scripts/app_health_check.sh 4321 120
      - name: Stop Development App
        if: always()
        run: |
          if [ -f .pidfile_dev ]; then
            kill "$(cat .pidfile_dev)"
          fi

  Docker-Start-Check:
    name: Check if Talawa Admin app starts in Docker
    runs-on: ubuntu-latest
    needs: [Code-Quality-Checks, Test-Application]
    if: github.actor != 'dependabot'
    steps:
      - name: Checkout the Repository
        uses: actions/checkout@v4

      - name: Generate `.env` File with Hardcoded Values
        run: |
          cat <<EOF > .env
          PORT=4321
          REACT_APP_TALAWA_URL=http://localhost:4000/graphql
          REACT_APP_USE_RECAPTCHA=
          REACT_APP_RECAPTCHA_SITE_KEY=
          REACT_APP_BACKEND_WEBSOCKET_URL=ws://localhost:4000/graphql
          ALLOW_LOGS=NO
          USE_DOCKER=YES
          DOCKER_PORT=4321
          EOF

      - name: Set up Docker
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: |
            image=moby/buildkit:latest

      - name: Build Docker images
        run: |
          set -e
          echo "Building Docker images..."
          docker compose -f docker/docker-compose.prod.yaml build
          docker compose -f docker/docker-compose.dev.yaml build
          echo "Docker images built successfully"

      - name: Run Docker Containers (Production)
        run: |
          set -e
          echo "Starting Docker container for production..."
          docker compose -f docker/docker-compose.prod.yaml up -d
          echo "Production Docker container started successfully"

      - name: Check if Talawa Admin App is running (Production)
        run: |
          chmod +x .github/workflows/scripts/app_health_check.sh
          .github/workflows/scripts/app_health_check.sh 4321 120 true

      - name: Stop prod Docker Containers
        if: always()
        run: |
          docker compose -f docker/docker-compose.prod.yaml down
          echo "Prod Docker container stopped and removed"

      - name: Run Docker Containers (Development)
        run: |
          set -e
          echo "Starting Docker container for development..."
          docker compose -f docker/docker-compose.dev.yaml up -d
          echo "Development Docker container started successfully"

      - name: Check if Talawa Admin App is running (Development)
        run: |
          chmod +x .github/workflows/scripts/app_health_check.sh
          .github/workflows/scripts/app_health_check.sh 4321 120 true

      - name: Stop dev Docker Containers
        if: always()
        run: |
          docker compose -f docker/docker-compose.dev.yaml down
          echo "Dev Docker containers stopped and removed"

  Test-Docusaurus-Deployment:
    name: Test Deployment to https://docs-admin.talawa.io
    runs-on: ubuntu-latest
    needs: [Docker-Start-Check, Start-App-Without-Docker]
    # Run only if the develop branch and not dependabot
    if: ${{ github.actor != 'dependabot[bot]' && github.event.pull_request.base.ref == 'develop' }}
    steps:
      - name: Checkout the Repository
        uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x' 
      - name: Install dependencies
        working-directory: ./docs
        run: npm install
      - name: Test building the website
        working-directory: ./docs
        run: npm run build

  Check-Target-Branch:
    if: ${{ github.actor != 'dependabot[bot]' }}
    name: Check Target Branch
    runs-on: ubuntu-latest
    steps:
      - name: Check if the target branch is develop
        if: github.event.pull_request.base.ref != 'develop'
        run: |
          echo "Error: Pull request target branch must be 'develop'. Please refer PR_GUIDELINES.md"
          echo "Error: Close this PR and try again."
          exit 1

  Python-Compliance:
    name: Check Python Code Style
    runs-on: ubuntu-latest
    needs: [Code-Quality-Checks]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python 3.11
        uses: actions/setup-python@v4
        with:
          python-version: 3.11

      - name: Cache pip packages
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install dependencies
        run: |
          python3 -m venv venv
          source venv/bin/activate
          python -m pip install --upgrade pip
          pip install -r .github/workflows/requirements.txt

      - name: Run Black Formatter Check
        run: |
          source venv/bin/activate
          black --check .

      - name: Run Flake8 Linter
        run: |
          source venv/bin/activate
          flake8 --docstring-convention google --ignore E402,E722,E203,F401,W503 .github

      - name: Run pydocstyle
        run: |
          source venv/bin/activate
          pydocstyle --convention=google --add-ignore=D415,D205 .github

      - name: Run docstring compliance check
        run: |
          source venv/bin/activate
          python .github/workflows/scripts/check_docstrings.py --directories .github
