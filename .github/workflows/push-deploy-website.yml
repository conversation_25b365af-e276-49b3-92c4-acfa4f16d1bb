##############################################################################
##############################################################################
#
# NOTE!
#
# Please read the README.md file in this directory that defines what should 
# be placed in this file
#
##############################################################################
##############################################################################

name: PUSH Workflow - Website Deployment

on:
  push:
    branches:
      - 'develop'
    paths:
      - docs/**

env:
  CODECOV_UNIQUE_NAME: CODECOV_UNIQUE_NAME-${{ github.run_id }}-${{ github.run_number }}
  
jobs:
  Deploy-Docusaurus:
    name: Deploy https://docs-admin.talawa.io website
    runs-on: ubuntu-latest
    # Run only if the develop branch and not dependabot
    if: ${{ github.actor != 'dependabot[bot]' }}
    environment:
      # This "name" has to be the repos' branch that contains 
      # the current active website. There must be an entry for
      # the same branch in the PalisadoesFoundation's
      # "Code and automation > Environments > gigithub-pages"
      # menu. The branch "name" must match the branch in the 
      # "on.push.branches" section at the top of this file
      name: develop
      url: https://docs-admin.talawa.io
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20.x' 
      - uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.DEPLOY_GITHUB_PAGES }}
      - name: Deploy to GitHub Pages
        env:
          USE_SSH: true
          GIT_USER: git
        working-directory: ./docs
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "gh-actions"
          yarn install --frozen-lockfile
          yarn deploy
 
