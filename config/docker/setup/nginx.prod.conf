server {
    listen 80;
    server_name admin-demo.talawa.io;

    # Redirect HTTP to HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl http2;
    server_name admin-demo.talawa.io;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/cert.pem;
    ssl_certificate_key /etc/ssl/private/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384';
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    root /usr/share/nginx/html;
    index index.html;

    # Serve frontend app
    location / {
        try_files $uri /index.html;
        expires 1h;
        add_header Cache-Control "public, max-age=3600";
    }

    # Reverse proxy to GraphQL API
    location /graphql {
        proxy_pass http://host.docker.internal:4000/graphql/;
        
        # Security headers
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;

        # Proxy headers
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static file caching
    location ~* \.(?:ico|css|js|gif|jpe?g|png|woff2?|eot|ttf|svg|mp4|webm|ogg|mp3|wav|flac|aac)$ {
        expires 6M;
        add_header Cache-Control "public, max-age=15552000, immutable";
    }

    # Brotli & Gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml+rss text/javascript;
    gzip_min_length 256;
    gzip_vary on;

    brotli on;
    brotli_types text/plain text/css application/json application/javascript text/xml application/xml+rss text/javascript image/svg+xml;
    brotli_comp_level 6;

    # Error handling
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
